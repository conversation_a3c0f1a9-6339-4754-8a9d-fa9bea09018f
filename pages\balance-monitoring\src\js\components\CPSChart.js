/**
 * CPS曲线图表组件
 */
import dayjs from "dayjs";
import * as echarts from "echarts";
import {
  processSimpleSeries,
  createChartOption,
  buildCPSDataUrl,
  buildRealtimeCPSDataUrl,
  getDefaultCPSDateRange,
  ColorOptions,
} from "../config/ChartConfig.js";

class CPSChart {
  constructor(containerId, data = null, dateRange = null) {
    console.log("🚀 ~ CPSChart ~ constructor ~ data:", data);
    this.container = document.getElementById(containerId);
    this.data = data;
    this.chart = null;
    this.dateRange = dateRange || getDefaultCPSDateRange();
    this.apiUrl = buildCPSDataUrl(
      this.dateRange.startDay,
      this.dateRange.endDay
    );
    this.realtimeApiUrl = buildRealtimeCPSDataUrl(); // 实时数据API URL
    this.refreshTimer = null; // 定时器
    this.refreshInterval = 60000; // 刷新间隔：60秒（1分钟）
    this.realtimeTimer = null; // 实时数据定时器
    this.realtimeInterval = 5000; // 实时数据刷新间隔：5秒
    this.realtimeData = { CPS1: null, CPS2: null }; // 存储最新的实时数据
    this.init();
  }

  init() {
    if (!this.container) {
      console.error("容器元素不存在");
      return;
    }

    // 初始化ECharts实例
    this.chart = echarts.init(this.container);

    // 如果没有传入数据，则从接口获取
    if (!this.data) {
      this.fetchData();
    } else {
      this.data = this.transformApiData(this.data);
      this.render();
    }

    // 启动定时刷新
    this.startAutoRefresh();

    // 启动实时数据获取
    this.startRealtimeDataFetch();

    // 添加窗口大小变化的监听器
    window.addEventListener("resize", () => {
      this.chart.resize();
    });
  }

  // 从接口获取数据
  async fetchData() {
    try {
      const response = await fetch(this.apiUrl);
      const result = await response.json();

      if (result.code === "0000" && result.data) {
        // 转换接口数据为图表格式
        this.data = this.transformApiData(result.data);
        this.render();
      } else {
        console.error("CPS接口返回错误:", result);
        // this.showError("数据获取失败");
      }
    } catch (error) {
      console.error("CPS接口调用失败:", error);
      // this.showError("网络请求失败");
    }
  }

  // 获取实时CPS数据
  async fetchRealtimeData() {
    try {
      const response = await fetch(this.realtimeApiUrl);
      const result = await response.json();

      if (result.code === "0000" && result.data) {
        this.processRealtimeData(result.data);
      } else {
        console.error("实时CPS接口返回错误:", result);
      }
    } catch (error) {
      console.error("实时CPS接口调用失败:", error);
    }
  }

  // 处理实时数据
  processRealtimeData(realtimeApiData) {
    // 更新实时数据存储
    if (Array.isArray(realtimeApiData)) {
      this.realtimeData.CPS1 = {
        value: this.formatNumber(200, realtimeApiData[0]),
        time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      };
      this.realtimeData.CPS2 = {
        value: this.formatNumber(23, realtimeApiData[1]),
        time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      };

      // 将实时数据插入到历史数据中
      this.insertRealtimeDataToHistory();

      // 重新渲染图表
      this.render();

      console.log("实时CPS数据已更新:", this.realtimeData);
    }
  }

  // 将实时数据插入到历史数据中
  insertRealtimeDataToHistory() {
    if (!this.data || !this.data.series) return;

    // 使用dayjs生成当前时间字符串，格式与CPS接口返回格式一致
    const timeString = dayjs().format("YYYY-MM-DD HH:mm:ss");
    const displayTime = this.formatTimeDisplay(timeString);

    // 查找最接近当前时间的时间点索引
    const currentTime = dayjs(timeString);
    let closestIndex = -1;
    let minDiff = Infinity;

    // 重新生成完整时间轴以确保一致性
    const completeTimeAxis = this.generateComplete7DayTimeAxis();

    // 查找最接近当前时间的预定义时间点
    completeTimeAxis.originalTimes.forEach((time, index) => {
      const timePoint = dayjs(time);
      const diff = Math.abs(currentTime.diff(timePoint, "minute"));
      if (diff < minDiff) {
        minDiff = diff;
        closestIndex = index;
      }
    });

    // 如果找到了最接近的时间点（在合理范围内，比如30分钟内），则更新该点的数据
    if (closestIndex !== -1 && minDiff <= 30) {
      this.data.series.forEach((series, index) => {
        if (index === 0 && this.realtimeData.CPS1) {
          // CPS1系列
          series.data[closestIndex] = this.realtimeData.CPS1.value;
        } else if (index === 1 && this.realtimeData.CPS2) {
          // CPS2系列
          series.data[closestIndex] = this.realtimeData.CPS2.value;
        }
      });
    } else {
      // 如果当前时间超出了预定义的时间轴范围，则添加新的时间点
      // 这种情况通常发生在实时数据超出了7天范围
      if (!this.data.xAxis.includes(displayTime)) {
        this.data.xAxis.push(displayTime);

        // 为每个系列添加新的数据点
        this.data.series.forEach((series, index) => {
          if (index === 0 && this.realtimeData.CPS1) {
            // CPS1系列
            series.data.push(this.realtimeData.CPS1.value);
          } else if (index === 1 && this.realtimeData.CPS2) {
            // CPS2系列
            series.data.push(this.realtimeData.CPS2.value);
          } else {
            // 其他系列添加null值
            series.data.push(null);
          }
        });
      }
    }
  }

  // 转换接口数据为图表格式
  transformApiData(apiData) {
    const { CPS1List = [], CPS2List = [] } = apiData;

    // 生成完整的7天时间轴
    const completeTimeAxis = this.generateComplete7DayTimeAxis();

    // 创建时间到值的映射
    const timeValueMap1 = new Map();
    const timeValueMap2 = new Map();

    CPS1List.forEach((item) => {
      timeValueMap1.set(item.time, this.formatNumber(item.value));
    });

    CPS2List.forEach((item) => {
      timeValueMap2.set(item.time, this.formatNumber(item.value));
    });

    // 根据完整时间轴生成对应的Y轴数据，没有数据的时间点设为null
    const cps1Data = completeTimeAxis.originalTimes.map(
      (time) => timeValueMap1.get(time) || null
    );
    const cps2Data = completeTimeAxis.originalTimes.map(
      (time) => timeValueMap2.get(time) || null
    );

    return {
      xAxis: completeTimeAxis.displayTimes,
      series: [
        {
          name: "当班CPS1",
          colorType: "green",
          showSymbol: true,
          data: cps1Data,
        },
        {
          name: "当班CPS2",
          colorType: "blue",
          showSymbol: true,
          data: cps2Data,
        },
      ],
    };
  }

  // 生成完整的7天时间轴
  generateComplete7DayTimeAxis() {
    const { startDay, endDay } = this.dateRange;

    // 使用dayjs生成完整的7天时间轴
    const startDate = dayjs(startDay);
    const endDate = dayjs(endDay);

    const originalTimes = [];
    const displayTimes = [];

    // 生成每天的时间点（这里可以根据需要调整时间间隔）
    // 目前生成每天的00:00:00, 04:00:00, 08:00:00, 12:00:00, 16:00:00, 20:00:00
    const timePoints = [
      "00:00:00",
      "04:00:00",
      "08:00:00",
      "12:00:00",
      "16:00:00",
      "20:00:00",
    ];

    let currentDate = startDate;
    while (currentDate.isSameOrBefore(endDate, "day")) {
      timePoints.forEach((timePoint) => {
        const fullTime = `${currentDate.format("YYYY-MM-DD")} ${timePoint}`;
        originalTimes.push(fullTime);
        displayTimes.push(this.formatTimeDisplay(fullTime));
      });
      currentDate = currentDate.add(1, "day");
    }

    return {
      originalTimes,
      displayTimes,
    };
  }

  // 格式化时间显示，只显示月份和时间，去除年份，精确到秒
  formatTimeDisplay(timeString) {
    // 输入格式: "2025-06-17 00:00:00"
    // 输出格式: "06-17 00:00:00"
    if (!timeString) return timeString;

    try {
      // 使用dayjs解析和格式化时间
      const date = dayjs(timeString);

      // 检查日期是否有效
      if (!date.isValid()) {
        console.warn("无效的时间格式:", timeString);
        return timeString;
      }

      // 返回月-日 时:分:秒格式
      return date.format("MM-DD HH:mm:ss");
    } catch (error) {
      console.warn("时间格式化失败:", timeString, error);
      return timeString;
    }
  }

  // 格式化数字为1位小数
  formatNumber(value) {
    if (value === null || value === undefined || value === "") {
      return null;
    }

    const num = parseFloat(value);
    if (isNaN(num)) {
      return null;
    }

    return parseFloat(num.toFixed(1));
  }

  // 显示错误信息
  showError(message) {
    if (this.chart) {
      this.chart.showLoading({
        text: message,
        color: "#c23531",
        textColor: "#c23531",
        maskColor: "rgba(255, 255, 255, 0.8)",
        zlevel: 0,
      });
    }
  }

  drawRealtimeDataView(data, seriesData) {
    // === 查找最后一个有效数据点的位置 ===
    let lastValidIndex1 = -1;
    let lastValidIndex2 = -1;

    // 从后往前查找最后一个有效的CPS1数据点
    for (let i = seriesData[0].data.length - 1; i >= 0; i--) {
      if (
        seriesData[0].data[i] !== null &&
        seriesData[0].data[i] !== undefined
      ) {
        lastValidIndex1 = i;
        break;
      }
    }

    // 从后往前查找最后一个有效的CPS2数据点
    for (let i = seriesData[1].data.length - 1; i >= 0; i--) {
      if (
        seriesData[1].data[i] !== null &&
        seriesData[1].data[i] !== undefined
      ) {
        lastValidIndex2 = i;
        break;
      }
    }

    // 如果没有找到有效数据点，则不显示实时数据视图
    if (lastValidIndex1 === -1 && lastValidIndex2 === -1) {
      return;
    }

    const lastCPS1Value =
      lastValidIndex1 !== -1 ? seriesData[0].data[lastValidIndex1] : null;
    const lastCPS2Value =
      lastValidIndex2 !== -1 ? seriesData[1].data[lastValidIndex2] : null;
    const lastCategory1 = lastValidIndex1 !== -1 ? data[lastValidIndex1] : null;
    const lastCategory2 = lastValidIndex2 !== -1 ? data[lastValidIndex2] : null;

    // 等待图表渲染完成后再计算像素坐标
    setTimeout(() => {
      let pos1 = null;
      let pos2 = null;

      // 只有当有有效数据时才计算像素坐标
      if (lastValidIndex1 !== -1 && lastCPS1Value !== null) {
        pos1 = this.chart.convertToPixel({ seriesIndex: 0 }, [
          lastCategory1,
          lastCPS1Value,
        ]);
      }

      if (lastValidIndex2 !== -1 && lastCPS2Value !== null) {
        pos2 = this.chart.convertToPixel({ seriesIndex: 1 }, [
          lastCategory2,
          lastCPS2Value,
        ]);
      }

      // 如果没有有效的位置信息，则不绘制
      if (!pos1 && !pos2) {
        return;
      }

      // 获取图表容器的尺寸
      const chartHeight = this.chart.getHeight();
      const chartWidth = this.chart.getWidth();

      // Group配置
      const groupWidth = 130;
      const groupHeight = 20;
      const minDistance = 30; // 两个group之间的最小距离
      const marginFromEdge = 50; // 距离图表边缘的最小距离

      // 构建图形元素数组
      const elements = [];

      // 如果有CPS1数据，添加CPS1相关元素
      if (pos1 && lastCPS1Value !== null) {
        // 计算CPS1 group位置
        const pos1Ratio = pos1[1] / chartHeight;
        let group1Top;
        if (pos1Ratio < 0.3) {
          group1Top = pos1[1] + 30;
        } else if (pos1Ratio > 0.7) {
          group1Top = pos1[1] - groupHeight - 30;
        } else {
          group1Top = pos1[1] - groupHeight / 2;
        }
        group1Top = Math.max(
          marginFromEdge,
          Math.min(group1Top, chartHeight - groupHeight - marginFromEdge)
        );

        const group1Center = {
          x: chartWidth - 10 - groupWidth / 2,
          y: group1Top + groupHeight / 2,
        };

        // CPS1 实时值显示组
        elements.push({
          type: "group",
          right: 10,
          top: group1Top,
          children: [
            {
              type: "rect",
              z: 1000,
              shape: { width: groupWidth, height: groupHeight, r: 3 },
              style: {
                fill: ColorOptions.green.colorTransparent2,
                stroke: ColorOptions.green.color,
                lineWidth: 2,
                shadowBlur: 8,
                shadowColor: "rgba(0,0,0,0.3)",
              },
            },
            {
              type: "text",
              z: 1001,
              left: 10,
              top: 5,
              style: {
                text: `CPS1实时值: ${lastCPS1Value}`,
                fill: ColorOptions.green.color,
                font: "12px sans-serif",
                textAlign: "left",
                textVerticalAlign: "top",
              },
            },
          ],
        });

        // CPS1 箭头
        elements.push({
          type: "line",
          z: 999,
          shape: {
            x1: group1Center.x,
            y1: group1Center.y,
            x2: pos1[0],
            y2: pos1[1],
          },
          style: {
            stroke: ColorOptions.green.color,
            lineWidth: 2,
            lineDash: [5, 5],
            opacity: 0.8,
          },
        });

        // CPS1 箭头头部
        elements.push({
          type: "polygon",
          z: 999,
          shape: {
            points: this.createArrowHead(
              group1Center.x,
              group1Center.y,
              pos1[0],
              pos1[1],
              6
            ),
          },
          style: { fill: ColorOptions.green.color, opacity: 0.8 },
        });
      }

      // 如果有CPS2数据，添加CPS2相关元素
      if (pos2 && lastCPS2Value !== null) {
        // 计算CPS2 group位置
        const pos2Ratio = pos2[1] / chartHeight;
        let group2Top;
        if (pos2Ratio < 0.3) {
          group2Top = pos2[1] + 30;
        } else if (pos2Ratio > 0.7) {
          group2Top = pos2[1] - groupHeight - 30;
        } else {
          group2Top = pos2[1] - groupHeight / 2;
        }
        group2Top = Math.max(
          marginFromEdge,
          Math.min(group2Top, chartHeight - groupHeight - marginFromEdge)
        );

        // 如果CPS1也存在，需要避免重叠
        if (pos1 && lastCPS1Value !== null) {
          const pos1Ratio = pos1[1] / chartHeight;
          let group1Top;
          if (pos1Ratio < 0.3) {
            group1Top = pos1[1] + 30;
          } else if (pos1Ratio > 0.7) {
            group1Top = pos1[1] - groupHeight - 30;
          } else {
            group1Top = pos1[1] - groupHeight / 2;
          }
          group1Top = Math.max(
            marginFromEdge,
            Math.min(group1Top, chartHeight - groupHeight - marginFromEdge)
          );

          const distance = Math.abs(group1Top - group2Top);
          if (distance < minDistance) {
            const midPoint = (group1Top + group2Top) / 2;
            const halfDistance = minDistance / 2;
            if (group1Top < group2Top) {
              group2Top = midPoint + halfDistance - groupHeight / 2;
            } else {
              group2Top = midPoint - halfDistance - groupHeight / 2;
            }
            group2Top = Math.max(
              marginFromEdge,
              Math.min(group2Top, chartHeight - groupHeight - marginFromEdge)
            );
          }
        }

        const group2Center = {
          x: chartWidth - 10 - groupWidth / 2,
          y: group2Top + groupHeight / 2,
        };

        // CPS2 实时值显示组
        elements.push({
          type: "group",
          right: 10,
          top: group2Top,
          children: [
            {
              type: "rect",
              z: 1000,
              shape: { width: groupWidth, height: groupHeight, r: 3 },
              style: {
                fill: ColorOptions.blue.colorTransparent2,
                stroke: ColorOptions.blue.color,
                lineWidth: 2,
                shadowBlur: 8,
                shadowColor: "rgba(0,0,0,0.3)",
              },
            },
            {
              type: "text",
              z: 1001,
              left: 10,
              top: 5,
              style: {
                text: `CPS2实时值: ${lastCPS2Value}`,
                fill: ColorOptions.blue.color,
                font: "12px sans-serif",
                textAlign: "left",
                textVerticalAlign: "top",
              },
            },
          ],
        });

        // CPS2 箭头
        elements.push({
          type: "line",
          z: 999,
          shape: {
            x1: group2Center.x,
            y1: group2Center.y,
            x2: pos2[0],
            y2: pos2[1],
          },
          style: {
            stroke: ColorOptions.blue.color,
            lineWidth: 2,
            lineDash: [5, 5],
            opacity: 0.8,
          },
        });

        // CPS2 箭头头部
        elements.push({
          type: "polygon",
          z: 999,
          shape: {
            points: this.createArrowHead(
              group2Center.x,
              group2Center.y,
              pos2[0],
              pos2[1],
              6
            ),
          },
          style: { fill: ColorOptions.blue.color, opacity: 0.8 },
        });
      }

      this.chart.setOption({
        graphic: { elements },
      });
    }, 100);
  }

  // 计算group的最佳位置，避免重叠
  calculateGroupPositions(
    pos1,
    pos2,
    chartHeight,
    groupHeight,
    minDistance,
    marginFromEdge
  ) {
    // 根据数据点的Y坐标确定group的初始位置
    let group1Top, group2Top;

    // 计算数据点在图表中的相对位置
    const pos1Ratio = pos1[1] / chartHeight;
    const pos2Ratio = pos2[1] / chartHeight;

    // 根据数据点位置智能确定group位置
    if (pos1Ratio < 0.3) {
      // 如果CPS1点在上部，group放在下方
      group1Top = pos1[1] + 30;
    } else if (pos1Ratio > 0.7) {
      // 如果CPS1点在下部，group放在上方
      group1Top = pos1[1] - groupHeight - 30;
    } else {
      // 如果CPS1点在中部，根据具体情况调整
      group1Top = pos1[1] - groupHeight / 2;
    }

    if (pos2Ratio < 0.3) {
      // 如果CPS2点在上部，group放在下方
      group2Top = pos2[1] + 30;
    } else if (pos2Ratio > 0.7) {
      // 如果CPS2点在下部，group放在上方
      group2Top = pos2[1] - groupHeight - 30;
    } else {
      // 如果CPS2点在中部，根据具体情况调整
      group2Top = pos2[1] - groupHeight / 2;
    }

    // 确保group不超出图表边界
    group1Top = Math.max(
      marginFromEdge,
      Math.min(group1Top, chartHeight - groupHeight - marginFromEdge)
    );
    group2Top = Math.max(
      marginFromEdge,
      Math.min(group2Top, chartHeight - groupHeight - marginFromEdge)
    );

    // 检查并解决重叠问题
    const distance = Math.abs(group1Top - group2Top);
    if (distance < minDistance) {
      // 如果重叠，调整位置
      const midPoint = (group1Top + group2Top) / 2;
      const halfDistance = minDistance / 2;

      if (group1Top < group2Top) {
        group1Top = midPoint - halfDistance - groupHeight / 2;
        group2Top = midPoint + halfDistance - groupHeight / 2;
      } else {
        group2Top = midPoint - halfDistance - groupHeight / 2;
        group1Top = midPoint + halfDistance - groupHeight / 2;
      }

      // 再次确保不超出边界
      group1Top = Math.max(
        marginFromEdge,
        Math.min(group1Top, chartHeight - groupHeight - marginFromEdge)
      );
      group2Top = Math.max(
        marginFromEdge,
        Math.min(group2Top, chartHeight - groupHeight - marginFromEdge)
      );
    }

    return {
      group1Top,
      group2Top,
    };
  }

  // 创建箭头头部的点坐标
  createArrowHead(x1, y1, x2, y2, size) {
    // 计算箭头方向
    const angle = Math.atan2(y2 - y1, x2 - x1);

    // 箭头头部的三个点
    const arrowHead = [
      [x2, y2], // 箭头尖端
      [
        x2 - size * Math.cos(angle - Math.PI / 6),
        y2 - size * Math.sin(angle - Math.PI / 6),
      ], // 左侧点
      [
        x2 - size * Math.cos(angle + Math.PI / 6),
        y2 - size * Math.sin(angle + Math.PI / 6),
      ], // 右侧点
    ];

    return arrowHead;
  }

  render() {
    if (!this.data) {
      this.showError("数据格式错误");
      return;
    }

    // 隐藏加载状态
    if (this.chart) {
      this.chart.hideLoading();
    }

    const { xAxis, series, yAxis } = this.data;

    // 使用公共配置处理简单系列数据
    const seriesData = processSimpleSeries(series);

    // 使用公共配置创建图表选项
    const option = createChartOption({
      xAxis,
      series: seriesData,
      yAxis,
      gridConfig: {
        top: "20%",
        bottom: "15%", // 增加底部空间以容纳旋转的标签
      },
      legendConfig: {},
      xAxisConfig: {
        interval: "auto", // 自动计算间隔
      },
    });

    this.chart.setOption(option);
    this.drawRealtimeDataView(xAxis, seriesData);
  }

  // 更新数据
  updateData(newData) {
    this.data = newData;
    this.render();
  }

  // 更新日期范围并重新获取数据
  updateDateRange(startDay, endDay) {
    this.dateRange = { startDay, endDay };
    this.apiUrl = buildCPSDataUrl(startDay, endDay);
    this.fetchData();
  }

  // 刷新数据（重新从接口获取）
  refreshData() {
    this.fetchData();
  }

  // 启动自动刷新
  startAutoRefresh() {
    // 清除现有定时器
    this.stopAutoRefresh();

    // 设置新的定时器
    this.refreshTimer = setInterval(() => {
      console.log("CPS图表自动刷新数据...");
      this.refreshData();
    }, this.refreshInterval);

    console.log(
      `CPS图表自动刷新已启动，间隔：${this.refreshInterval / 1000}秒`
    );
  }

  // 停止自动刷新
  stopAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
      console.log("CPS图表自动刷新已停止");
    }
  }

  // 设置刷新间隔
  setRefreshInterval(intervalMs) {
    this.refreshInterval = intervalMs;
    // 如果定时器正在运行，重新启动以应用新间隔
    if (this.refreshTimer) {
      this.startAutoRefresh();
    }
  }

  // 启动实时数据获取
  startRealtimeDataFetch() {
    // 清除现有定时器
    this.stopRealtimeDataFetch();

    // 立即获取一次实时数据
    this.fetchRealtimeData();

    // 设置定时器
    this.realtimeTimer = setInterval(() => {
      console.log("获取实时CPS数据...");
      this.fetchRealtimeData();
    }, this.realtimeInterval);

    console.log(
      `实时CPS数据获取已启动，间隔：${this.realtimeInterval / 1000}秒`
    );
  }

  // 停止实时数据获取
  stopRealtimeDataFetch() {
    if (this.realtimeTimer) {
      clearInterval(this.realtimeTimer);
      this.realtimeTimer = null;
      console.log("实时CPS数据获取已停止");
    }
  }

  // 设置实时数据获取间隔
  setRealtimeInterval(intervalMs) {
    this.realtimeInterval = intervalMs;
    // 如果定时器正在运行，重新启动以应用新间隔
    if (this.realtimeTimer) {
      this.startRealtimeDataFetch();
    }
  }

  // 销毁图表
  destroy() {
    // 停止自动刷新
    this.stopAutoRefresh();

    // 停止实时数据获取
    this.stopRealtimeDataFetch();

    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
    window.removeEventListener("resize", this.chart.resize);
  }
}

export default CPSChart;
