html {
  box-sizing: border-box;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}
body {
  position: relative;
  background-color: #020f22;
  color: #c0c0c0;
  margin: 0;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

header {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 13vh;
  background: url(../images/Title.webp) no-repeat center center;
  background-size: contain;
}
.main-grid {
  padding: 7vh 1vh 1vh 1vh;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  gap: 1vh;
}

.top-layout {
  flex: 5;
  min-height: 0;
}

.bottom-layout {
  flex: 4;
  overflow: auto;
  padding: 1vh;
}

.charts-row {
  display: flex;
  width: 100%;
  flex-wrap: nowrap;
  gap: 1vh;
}

.balance-chart-layout .chart-title {
  background-image: url(../images/BalanceBg.webp);
}

.balance-chart-container,
.wind-chart-container,
.solar-chart-container {
  height: 100%;
  position: relative;
  overflow: hidden;
  margin: 0 1.5vh 1vh 1.5vh;
}

.chart-title {
  height: 5vh;
  width: 100%;
  background-size: 100% auto;
  background-position: left top;
  background-repeat: no-repeat;
  flex-shrink: 0;
}

.charts-column {
  display: flex;
  flex-direction: column;
  width: 40%;
  gap: 1vh;
}

.wind-chart-layout .chart-title {
  background-image: url(../images/WindBg.webp);
}

.solar-chat-layout .chart-title {
  background-image: url(../images/SolarBg.webp);
}
.chart-container {
  overflow: hidden;
  position: relative;
}
.top-layout iframe {
  position: absolute;
  border: none;
  transform-origin: 0 0;
}

/* 
 * 布局长宽 2356*1351 1.745
 * 宽度占 0.65
 * 高度占 0.479 
 * 左边: 0.18 
 * 上边: 0.069
 * 2356/0.65 = 3625
 * 3625*0.65 = 2356 
 * 2356/1.745 = 1350
 * 1350 / 0.479 = 2818
 510/792
 */
.balance-chart-container iframe {
  width: 4200px; /* 设置宽度以保证清晰度和覆盖区域 */
  height: 3150px; /* 必须够大以包含你想要显示的部分 */
  top: -160px; /* 向上移动，让目标区域显示出来 */
  left: -625px; /* 向左移动，居中你想要的区域 */
  transform: scale(0.86); /*按照原尺寸然后 缩小以适应容器 */
}

/* 
 * 布局长宽 1560*565  2.76 0.362
 * 宽度占 0.496  
 * 高度占 0.252
 * 左边: 0.006 
 * 上边: 0.733
 * 1560 / 0.496 = 3145.16
 * 3145.16 * 0.496 = 1560
 * 1560 / 2.76 = 565.22
 * 565.22 / 0.252 = 2242.93
 */
.wind-chart-container iframe {
  width: 4200px; /* 设置宽度以保证清晰度和覆盖区域 */
  height: 3150px; /* 必须够大以包含你想要显示的部分 */
  top: -1885.06px; /* 向上移动，让目标区域显示出来 */
  left: -1600.6px; /* 向左移动，居中你想要的区域 */
  transform: scale(0.74); /*按照原尺寸然后 缩小以适应容器 */
}

/* 
 * 布局长宽 1560*565  2.76 0.362
 * 宽度占 0.496  
 * 高度占 0.252
 * 左边: 0.509
 * 上边: 0.733
 * 1560 / 0.496 = 3145.16
 * 3145.16 * 0.496 = 1560
 * 1560 / 2.76 = 565.22
 * 565.22 / 0.252 = 2242.93
 */

.solar-chart-container iframe {
  width: 4200px; /* 设置宽度以保证清晰度和覆盖区域 */
  height: 3150px; /* 必须够大以包含你想要显示的部分 */
  top: -1885.06px; /* 向上移动，让目标区域显示出来 */
  left: -1600.6px; /* 向左移动，居中你想要的区域 */
  transform: scale(0.74); /*按照原尺寸然后 缩小以适应容器 */
}

.chart-container,
.table-container,
.info-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  background: #082e6c11;
  border-radius: 8px;
}

.section-table-layout .chart-title {
  background-image: url(../images/SectionBg.webp);
}

/* 右侧列样式 */
.right-column {
  width: 40%;
}

#section-table table {
  padding: 0 1.5vh;
  width: 100%;
  font-size: 1rem;
  border-collapse: separate;
  border-spacing: 0px 0.6vh;
}

#section-table th,
#section-table td {
  padding: 1.5vh;
  text-align: center;
}

#section-table th {
  background: linear-gradient(to bottom, #042e58 0%, #011e3b 15%, #003f6b 100%);
  font-weight: bold;
}

#section-table th span {
  background-image: linear-gradient(
    to bottom,
    #ffffff 0%,
    #ddf1fb 30%,
    #a9dcfe 60%,
    #64c8f5 100%
  ); /* 背景渐变 */
  -webkit-background-clip: text; /* 文字渐变 */
  color: transparent; /* 文字透明 */
}

#section-table td {
  color: #fff;
  background: linear-gradient(
    to bottom,
    #042e5893 0%,
    #011e3b93 15%,
    #003f6b93 100%
  );
}

#cps-chart {
  flex: 2;
  padding: 1.5vh;
}
.info-container {
  display: flex;
  flex: 1;
  flex-direction: row;
  align-items: center;
}

.info-box {
  text-align: center;
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  position: relative;
  height: 5rem;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: 50%;
}

.current-cost {
  background-image: url(../images/CurrentCost.webp);
}

.real-time-ace {
  background-image: url(../images/RealTimeACE.webp);
}

.current-cost .info-box-value {
  background-image: linear-gradient(
    to bottom,
    #ffffff 0%,
    #d4fafd 30%,
    #9cf6fe 60%,
    #00586a 100%
  ); /* 背景渐变 */
  -webkit-background-clip: text; /* 文字渐变 */
  color: transparent; /* 文字透明 */
}

.real-time-ace .info-box-value {
  background-image: linear-gradient(
    to bottom,
    #ffffff 0%,
    #e9f0d2 30%,
    #f9da79 60%,
    #543e09 100%
  ); /* 背景渐变 */
  -webkit-background-clip: text; /* 文字渐变 */
  color: transparent; /* 文字透明 */
}

.info-box-value {
  position: absolute;
  bottom: 14.5%;
  left: 38%;
  font-size: 1.2rem;
  font-weight: bold;
  opacity: 0.8;
}

/* 数据标签样式 */
.data-label {
  position: absolute;
  background-color: rgba(10, 26, 51, 0.7);
  border: 1px solid #1a3a66;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.9em;
  color: #fff;
  pointer-events: none;
}

/* 当前值标记样式 */
.current-value {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #fff;
  border: 2px solid;
}

@media screen and (min-width: 2000px) {
  html {
    font-size: 18px;
  }

  header {
    height: 14vh;
  }

  .main-grid {
    padding: 8vh 1vh 1vh 1vh;
  }

  #section-table table {
    font-size: 1.5rem;
  }
  .info-box {
    height: 6rem;
  }
  .info-box-value {
    font-size: 1.5rem;
    left: 41%;
    bottom: 12%;
  }
}

@media screen and (min-width: 3000px) {
  html {
    font-size: 20px;
  }

  header {
    height: 14vh;
  }

  .main-grid {
    padding: 8vh 1vh 1vh 1vh;
  }

  #section-table table {
    font-size: 1.5rem;
  }
  .info-box {
    height: 7rem;
  }
  .info-box-value {
    font-size: 1.8rem;
    left: 41%;
    bottom: 10%;
  }
}

@media screen and (min-width: 4000px) {
  html {
    font-size: 28px;
  }

  .info-box-value {
    font-size: 1.8rem;
    left: 39%;
  }
}
