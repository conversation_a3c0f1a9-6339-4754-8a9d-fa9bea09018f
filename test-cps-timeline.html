<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CPS时间轴测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #0a1a33;
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .chart-container {
            width: 100%;
            height: 400px;
            margin: 20px 0;
            border: 1px solid #1a3a66;
            border-radius: 8px;
        }
        .info {
            background-color: #1a3a66;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-buttons {
            margin: 20px 0;
        }
        .test-buttons button {
            background-color: #448df5;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-buttons button:hover {
            background-color: #5ca3ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CPS时间轴完整性测试</h1>
        
        <div class="info">
            <h3>测试说明：</h3>
            <p>1. 此测试验证CPS图表是否能显示完整的7天时间轴</p>
            <p>2. 即使数据不足，时间轴也应该显示完整的7天</p>
            <p>3. 没有数据的时间点应该显示为空白</p>
            <p>4. 实时数据应该能正确插入到最接近的时间点</p>
        </div>

        <div class="test-buttons">
            <button onclick="testEmptyData()">测试空数据</button>
            <button onclick="testPartialData()">测试部分数据</button>
            <button onclick="testFullData()">测试完整数据</button>
            <button onclick="testRealtimeData()">测试实时数据</button>
        </div>

        <div id="cps-chart" class="chart-container"></div>
        
        <div class="info">
            <h3>当前测试状态：</h3>
            <p id="test-status">等待测试...</p>
        </div>
    </div>

    <script type="module">
        import CPSChart from './pages/balance-monitoring/src/js/components/CPSChart.js';

        let cpsChart = null;

        // 初始化图表
        function initChart() {
            if (cpsChart) {
                cpsChart.destroy();
            }
            cpsChart = new CPSChart('cps-chart');
        }

        // 测试空数据
        window.testEmptyData = function() {
            document.getElementById('test-status').textContent = '测试空数据 - 应该显示完整7天时间轴，但没有数据点';
            initChart();
            
            // 模拟空数据
            const emptyData = {
                CPS1List: [],
                CPS2List: []
            };
            
            cpsChart.data = cpsChart.transformApiData(emptyData);
            cpsChart.render();
        };

        // 测试部分数据
        window.testPartialData = function() {
            document.getElementById('test-status').textContent = '测试部分数据 - 应该显示完整7天时间轴，只有部分时间点有数据';
            initChart();
            
            // 模拟部分数据（只有前3天的部分数据）
            const today = new Date();
            const threeDaysAgo = new Date(today.getTime() - 3 * 24 * 60 * 60 * 1000);
            
            const partialData = {
                CPS1List: [
                    {
                        time: threeDaysAgo.toISOString().split('T')[0] + ' 08:00:00',
                        value: 150.5
                    },
                    {
                        time: threeDaysAgo.toISOString().split('T')[0] + ' 16:00:00',
                        value: 180.2
                    }
                ],
                CPS2List: [
                    {
                        time: threeDaysAgo.toISOString().split('T')[0] + ' 12:00:00',
                        value: 25.8
                    }
                ]
            };
            
            cpsChart.data = cpsChart.transformApiData(partialData);
            cpsChart.render();
        };

        // 测试完整数据
        window.testFullData = function() {
            document.getElementById('test-status').textContent = '测试完整数据 - 应该显示完整7天时间轴，大部分时间点都有数据';
            initChart();
            
            // 模拟完整数据
            const fullData = {
                CPS1List: [],
                CPS2List: []
            };
            
            // 生成7天的测试数据
            for (let day = 0; day < 7; day++) {
                const date = new Date();
                date.setDate(date.getDate() - (6 - day));
                const dateStr = date.toISOString().split('T')[0];
                
                // 每天生成几个数据点
                const timePoints = ['00:00:00', '08:00:00', '16:00:00'];
                timePoints.forEach(time => {
                    fullData.CPS1List.push({
                        time: `${dateStr} ${time}`,
                        value: 150 + Math.random() * 50
                    });
                    fullData.CPS2List.push({
                        time: `${dateStr} ${time}`,
                        value: 20 + Math.random() * 15
                    });
                });
            }
            
            cpsChart.data = cpsChart.transformApiData(fullData);
            cpsChart.render();
        };

        // 测试实时数据
        window.testRealtimeData = function() {
            document.getElementById('test-status').textContent = '测试实时数据 - 先加载部分历史数据，然后模拟实时数据更新';
            
            // 先加载部分数据
            testPartialData();
            
            // 3秒后开始模拟实时数据
            setTimeout(() => {
                // 模拟实时数据更新
                const mockRealtimeData = [
                    200 + Math.random() * 50,  // CPS1
                    30 + Math.random() * 10    // CPS2
                ];
                
                cpsChart.processRealtimeData(mockRealtimeData);
                document.getElementById('test-status').textContent += ' - 实时数据已更新';
            }, 3000);
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            testPartialData(); // 默认显示部分数据测试
        });
    </script>
</body>
</html>
