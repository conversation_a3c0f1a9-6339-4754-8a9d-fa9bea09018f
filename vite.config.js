import { defineConfig } from "vite";
import { resolve } from "path";

// 多页面配置
const pages = {
  main: {
    entry: resolve(__dirname, "pages/balance-monitoring/index.html"),
    name: "balance-monitoring",
    title: "全网平衡监视系统",
    outputDir: "balance-monitoring", // 输出目录名
  },
  // 示例：添加新页面只需要在这里配置
  about: {
    entry: resolve(__dirname, "pages/about/index.html"),
    name: "about",
    title: "关于页面",
    outputDir: "about", // 输出目录名
  },
};

// 公共依赖库配置 - 这些库将被提取到vendor chunk中
const vendorLibs = ["echarts", "dayjs", "d3"];

// 构建多页面入口配置
function buildMultiPageInput() {
  const input = {};
  Object.keys(pages).forEach((key) => {
    const page = pages[key];
    input[key] = page.entry;
  });
  return input;
}

// 根据入口文件路径获取页面配置
function getPageConfigByEntry(entryName) {
  const pageKey = Object.keys(pages).find((key) => key === entryName);
  return pageKey ? pages[pageKey] : null;
}

export default defineConfig({
  // 开发服务器配置
  server: {
    port: 3000,
    open: "/pages/balance-monitoring/index.html",
    // 代理配置
    proxy: {
      "/dwyztApp/dwyzt": {
        target: "http://*************:8087/mock/189/dwyzt",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/dwyztApp\/dwyzt/, ""),
      },
    },
  },

  // 优化配置
  optimizeDeps: {
    // 预构建依赖，提高开发时的加载速度
    include: vendorLibs,
    // 强制预构建
    force: false,
  },
  // 构建配置
  build: {
    outDir: "dist",
    sourcemap: false,
    minify: "terser",
    // 分包策略
    rollupOptions: {
      input: buildMultiPageInput(),
      output: {
        // 公共依赖库放在共享的vendor目录
        chunkFileNames: (chunkInfo) => {
          // 公共第三方库放在共享vendor目录
          if (vendorLibs.some((lib) => chunkInfo.name.includes(lib))) {
            return `shared/vendor/[name].[hash].js`;
          }

          // 其他第三方库也放在共享vendor目录
          if (chunkInfo.name === "vendor") {
            return `shared/vendor/[name].[hash].js`;
          }

          // 获取页面配置，默认使用第一个页面
          const pageConfig =
            getPageConfigByEntry(chunkInfo.name) || Object.values(pages)[0];
          const outputDir = pageConfig.outputDir;

          // 业务代码chunk放在对应页面的chunks目录
          return `pages/${outputDir}/js/chunks/[name].[hash].js`;
        },

        // 入口文件配置 - 按页面分组
        entryFileNames: (chunkInfo) => {
          // 根据入口文件名获取页面配置
          const pageConfig =
            getPageConfigByEntry(chunkInfo.name) || Object.values(pages)[0];
          const outputDir = pageConfig.outputDir;

          return `pages/${outputDir}/js/[name].[hash].js`;
        },

        // 静态资源文件配置 - 按页面分组
        assetFileNames: (assetInfo) => {
          const fileName = assetInfo.names?.[0] || "unknown";
          const info = fileName.split(".");
          const ext = info[info.length - 1];

          // 默认使用第一个页面配置
          const pageConfig = Object.values(pages)[0];
          const outputDir = pageConfig.outputDir;

          // 图片资源
          if (/\.(png|jpe?g|gif|svg|webp|ico)$/i.test(fileName)) {
            return `pages/${outputDir}/assets/images/[name].[hash].[ext]`;
          }
          // 字体资源
          if (/\.(woff2?|eot|ttf|otf)$/i.test(fileName)) {
            return `pages/${outputDir}/assets/fonts/[name].[hash].[ext]`;
          }
          // CSS文件
          if (ext === "css") {
            return `pages/${outputDir}/css/[name].[hash].[ext]`;
          }
          // 其他资源
          return `pages/${outputDir}/assets/[name].[hash].[ext]`;
        },

        // 手动分包配置 - 优化代码切分
        manualChunks: (id) => {
          // 第三方库分包 - 提取到共享vendor
          if (id.includes("node_modules")) {
            // 大型库单独分包到共享目录
            if (id.includes("echarts")) {
              return "echarts";
            }
            if (id.includes("dayjs")) {
              return "dayjs";
            }
            if (id.includes("d3")) {
              return "d3";
            }
            // 其他第三方库合并到vendor
            return "vendor";
          }

          // 业务代码分包 - 按功能模块分离
          if (id.includes("src/js/")) {
            // 根据文件路径进行分包
            if (id.includes("components/")) {
              return "components";
            }
            if (id.includes("config/")) {
              return "config";
            }
            if (id.includes("utils/")) {
              return "utils";
            }
            if (id.includes("api/")) {
              return "api";
            }
          }
        },
      },
    },

    // 压缩配置
    terserOptions: {
      compress: {
        drop_console: true, // 生产环境移除console
        drop_debugger: true, // 生产环境移除debugger
      },
    },

    // 分包大小警告配置
    chunkSizeWarningLimit: 500, // 降低警告阈值，鼓励更细粒度的分包
  },

  // 基础路径配置
  base: "./",

  // 静态资源处理
  assetsInclude: ["**/*.webp"],
});
